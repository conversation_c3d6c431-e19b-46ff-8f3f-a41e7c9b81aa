const { app, <PERSON><PERSON><PERSON>W<PERSON>ow, B<PERSON>er<PERSON><PERSON><PERSON>, <PERSON>ray, Menu, ipcMain, screen, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const Store = require('electron-store');
const AutoLaunch = require('auto-launch');
const PlaylistManager = require('./playlist-manager');
const AudioController = require('./audio-controller');
const YouTubeAdSkipper = require('./youtube-ad-skipper');
const VideoProgressMonitor = require('./video-progress-monitor');
const executionCollector = require('./execution-collector');


// Initialize store for persistent data
const store = new Store();

// Global references
let mainWindow = null;
let tray = null;
let youtubeView = null;
let spotifyView = null;
let isMinimized = false;
let isMuted = false;
let playlistManager = null;
let audioController = null;
let youtubeAdSkipper = null;
let videoProgressMonitor = null;
let appInstance = null; // Singleton instance

// Auto-launch setup
const autoLauncher = new AutoLaunch({
  name: 'YSViewer',
  path: app.getPath('exe')
});

class YSViewerApp {
  constructor() {
    if (appInstance) {
      return appInstance;
    }
    appInstance = this;
    this.initializeApp();
  }

  static getInstance() {
    if (!appInstance) {
      appInstance = new YSViewerApp();
    }
    return appInstance;
  }

  async initializeApp() {
    // Initialize managers
    playlistManager = new PlaylistManager();
    audioController = new AudioController();
    youtubeAdSkipper = new YouTubeAdSkipper();
    videoProgressMonitor = new VideoProgressMonitor();

    // Set mute state but don't apply yet (no web contents registered)
    isMuted = true;
    console.log('Application will start muted by default');

    // Setup auto-launch
    await this.setupAutoLaunch();

    // Create main window
    this.createMainWindow();

    // Create system tray
    this.createSystemTray();

    // Setup browser views
    this.setupBrowserViews();

    // Now apply mute state after web contents are registered
    await audioController.setMute(true);
    console.log('Application started muted by default - mute applied to web contents');

    // Start playlist management
    await this.startPlaylistManagement();
  }

  async setupAutoLaunch() {
    try {
      const isEnabled = await autoLauncher.isEnabled();
      if (!isEnabled) {
        await autoLauncher.enable();
        console.log('Auto-launch enabled');
      }
    } catch (error) {
      console.error('Failed to setup auto-launch:', error);
    }
  }

  createMainWindow() {
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    
    mainWindow = new BrowserWindow({
      width: Math.min(1400, width - 100),
      height: Math.min(800, height - 100),
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        webSecurity: false
      },
      icon: path.join(__dirname, '../assets/icon.png'),
      show: false,
      frame: true,
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default'
    });

    // Load the main HTML file
    mainWindow.loadFile(path.join(__dirname, 'electron-index.html'));

    // Show window when ready
    mainWindow.once('ready-to-show', () => {
      mainWindow.show();
      if (process.argv.includes('--dev')) {
        mainWindow.webContents.openDevTools();
      }
    });

    // Handle window minimize - system minimize button should minimize to tray
    mainWindow.on('minimize', (event) => {
      event.preventDefault();
      this.minimizeToTray();
    });
  }

  createSystemTray() {
    try {
      const { nativeImage } = require('electron');

      // Try to use the app icon first, fallback to generated icon
      let trayIcon;
      try {
        // Try to load the app icon for tray
        const iconPath = path.join(__dirname, '../assets/icon.png');
        trayIcon = nativeImage.createFromPath(iconPath);

        // If icon is empty or invalid, create a simple one
        if (trayIcon.isEmpty()) {
          throw new Error('Icon file not found or invalid');
        }

        // Resize for tray (Windows typically uses 16x16)
        trayIcon = trayIcon.resize({ width: 16, height: 16 });
      } catch (iconError) {
        console.log('Using fallback tray icon:', iconError.message);

        // Create a simple 16x16 green square icon as fallback
        const size = 16;
        const buffer = Buffer.alloc(size * size * 4);

        // Fill with green color
        for (let i = 0; i < buffer.length; i += 4) {
          buffer[i] = 76;     // R
          buffer[i + 1] = 175; // G
          buffer[i + 2] = 80;  // B
          buffer[i + 3] = 255; // A
        }

        trayIcon = nativeImage.createFromBuffer(buffer, { width: size, height: size });
      }

      tray = new Tray(trayIcon);
      console.log('System tray created successfully');
    } catch (error) {
      console.log('Tray creation failed, continuing without tray:', error.message);
      tray = null;
    }

    // Only create menu if tray was created successfully
    if (!tray) {
      console.log('Skipping tray menu creation - no tray available');
      return;
    }

    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'YSViewer',
        type: 'normal',
        enabled: false
      },
      { type: 'separator' },
      {
        label: isMuted ? 'Unmute Application' : 'Mute Application',
        type: 'normal',
        click: () => this.toggleSystemAudio()
      },
      { type: 'separator' },
      {
        label: 'Show Window',
        type: 'normal',
        click: () => this.showMainWindow()
      },
      {
        label: 'Minimize to Dot',
        type: 'normal',
        click: () => this.minimizeToDot()
      },
      { type: 'separator' },
      {
        label: 'Quit',
        type: 'normal',
        click: () => {
          app.isQuiting = true;
          app.quit();
        }
      }
    ]);

    tray.setContextMenu(contextMenu);
    tray.setToolTip('YSViewer - YouTube Player');

    // Handle tray click
    tray.on('click', () => {
      if (mainWindow.isVisible()) {
        this.minimizeToTray();
      } else {
        this.showMainWindow();
      }
    });
  }

  setupBrowserViews() {
    const bounds = mainWindow.getBounds();
    const viewWidth = Math.floor(bounds.width / 2);
    const viewHeight = bounds.height - 170; // Leave space for controls and address bars (100 + 70)

    // YouTube view
    youtubeView = new BrowserView({
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    mainWindow.setBrowserView(youtubeView);
    youtubeView.setBounds({ x: 0, y: 170, width: viewWidth, height: viewHeight }); // Start at y: 170 to account for address bars
    youtubeView.webContents.loadURL('https://www.youtube.com');

    // Register YouTube view for audio control, ad skipping, and progress monitoring
    audioController.registerWebContents(youtubeView.webContents);
    youtubeAdSkipper.registerWebContents(youtubeView.webContents);
    videoProgressMonitor.registerWebContents(youtubeView.webContents);

    // Set up video switch callback
    videoProgressMonitor.setVideoSwitchCallback((progressInfo) => {
      console.log(`Video switch triggered at ${progressInfo.progress.toFixed(2)}%`);
      this.playNextVideo();
    });

    console.log('YouTube web contents registered for audio control, ad skipping, and progress monitoring');

    // Spotify view
    spotifyView = new BrowserView({
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    mainWindow.addBrowserView(spotifyView);
    spotifyView.setBounds({ x: viewWidth, y: 170, width: viewWidth, height: viewHeight }); // Start at y: 170 to account for address bars

    // Register Spotify view for audio control
    audioController.registerWebContents(spotifyView.webContents);
    console.log('Spotify web contents registered for audio control');

    // Load Spotify web player
    spotifyView.webContents.loadURL('https://open.spotify.com/');

    // Handle window resize
    mainWindow.on('resize', () => {
      const newBounds = mainWindow.getBounds();
      const newViewWidth = Math.floor(newBounds.width / 2);
      const newViewHeight = newBounds.height - 170; // Account for controls and address bars

      youtubeView.setBounds({ x: 0, y: 170, width: newViewWidth, height: newViewHeight });
      spotifyView.setBounds({ x: newViewWidth, y: 170, width: newViewWidth, height: newViewHeight });
    });

    // Setup URL tracking for address bars
    this.setupURLTracking();

    // Handle external links and auto-accept cookies
    [youtubeView, spotifyView].forEach((view, index) => {
      const platform = index === 0 ? 'YouTube' : 'Spotify';

      view.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
      });

      // Auto-accept cookies when page loads
      view.webContents.on('dom-ready', () => {
        // Re-apply mute state when DOM is ready
        if (isMuted) {
          view.webContents.setAudioMuted(true);
          console.log(`Re-applied mute state to ${platform} on DOM ready`);
        }

        // Restart ad detection for YouTube
        if (platform === 'YouTube' && youtubeAdSkipper) {
          youtubeAdSkipper.startAdDetection();
        }

        const appInstance = YSViewerApp.getInstance();
        appInstance.autoAcceptCookies(view.webContents, platform);
      });

      // Also try after navigation
      view.webContents.on('did-finish-load', () => {
        // Re-apply mute state after page loads
        if (isMuted) {
          view.webContents.setAudioMuted(true);
          console.log(`Re-applied mute state to ${platform} after page load`);
        }

        // Restart ad detection for YouTube after page loads
        if (platform === 'YouTube' && youtubeAdSkipper) {
          setTimeout(() => {
            youtubeAdSkipper.startAdDetection();
          }, 1000); // Wait 1 second for page to stabilize
        }

        setTimeout(() => {
          const appInstance = YSViewerApp.getInstance();
          appInstance.autoAcceptCookies(view.webContents, platform);
        }, 2000); // Wait 2 seconds for cookie banners to appear
      });
    });
  }

  setupURLTracking() {
    // Track YouTube URL changes
    if (youtubeView && youtubeView.webContents) {
      youtubeView.webContents.on('did-navigate', (event, url) => {
        this.updateAddressBar('youtube', url);
      });

      youtubeView.webContents.on('did-navigate-in-page', (event, url) => {
        this.updateAddressBar('youtube', url);
      });

      youtubeView.webContents.on('did-finish-load', () => {
        const url = youtubeView.webContents.getURL();
        this.updateAddressBar('youtube', url);
      });
    }

    // Track Spotify URL changes
    if (spotifyView && spotifyView.webContents) {
      spotifyView.webContents.on('did-navigate', (event, url) => {
        this.updateAddressBar('spotify', url);
      });

      spotifyView.webContents.on('did-navigate-in-page', (event, url) => {
        this.updateAddressBar('spotify', url);
      });

      spotifyView.webContents.on('did-finish-load', () => {
        const url = spotifyView.webContents.getURL();
        this.updateAddressBar('spotify', url);
      });
    }
  }

  updateAddressBar(platform, url) {
    try {
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('update-address-bar', { platform, url });
      }
    } catch (error) {
      console.error(`Failed to update ${platform} address bar:`, error);
    }
  }

  async startPlaylistManagement() {
    try {
      await playlistManager.initialize();
      
      // Start playing immediately
      setTimeout(() => {
        this.playNextVideo();
      }, 3000); // Wait 3 seconds for views to load

      // Set up periodic playlist checking
      setInterval(() => {
        this.checkAndPlayNext();
      }, 3600000); // Check every hour
    } catch (error) {
      console.error('Failed to start playlist management:', error);
    }
  }

  async playNextVideo() {
    try {
      // Use improved random selection - try mandatory first, then random pool
      let nextYouTubeVideo = await playlistManager.getNextMandatoryVideo('YouTube');

      // If no mandatory videos available, try truly random selection
      if (!nextYouTubeVideo) {
        nextYouTubeVideo = await playlistManager.getAnyRandomVideo('YouTube');
      }

      // Play YouTube video if available
      if (nextYouTubeVideo && (nextYouTubeVideo.url.includes('youtube.com') || nextYouTubeVideo.url.includes('youtu.be'))) {
        console.log('Playing YouTube video:', nextYouTubeVideo.url);

        // Try to find and click recommended video link first
        const clickedRecommended = await this.tryClickRecommendedVideo(nextYouTubeVideo.url);

        if (!clickedRecommended) {
          // Fall back to direct URL loading if no recommended video found
          youtubeView.webContents.loadURL(nextYouTubeVideo.url);
        }

        // Notify progress monitor of new video
        if (videoProgressMonitor) {
          videoProgressMonitor.setCurrentVideo(nextYouTubeVideo.url, nextYouTubeVideo);
        }
      }

      // If still no videos, try any available video from any pool
      if (!nextYouTubeVideo) {
        const anyVideo = await playlistManager.getAnyRandomVideo();
        if (anyVideo) {
          console.log('Playing any available video:', anyVideo.url);

          if (anyVideo.url.includes('youtube.com') || anyVideo.url.includes('youtu.be')) {
            // Try to find and click recommended video link first
            const clickedRecommended = await this.tryClickRecommendedVideo(anyVideo.url);

            if (!clickedRecommended) {
              // Fall back to direct URL loading if no recommended video found
              youtubeView.webContents.loadURL(anyVideo.url);
            }

            // Notify progress monitor of new video
            if (videoProgressMonitor) {
              videoProgressMonitor.setCurrentVideo(anyVideo.url, anyVideo);
            }
          }
        } else {
          console.log('No videos available to play');
        }
      }
    } catch (error) {
      console.error('Failed to play next video:', error);
    }
  }

  async checkAndPlayNext() {
    try {
      const shouldPlay = await playlistManager.shouldPlayNext();
      if (shouldPlay) {
        this.playNextVideo();
      }
    } catch (error) {
      console.error('Failed to check playlist:', error);
    }
  }

  /**
   * Try to find and click a recommended video link on the current YouTube page
   * @param {string} targetUrl - The URL we want to navigate to
   * @returns {boolean} - True if a recommended video was clicked, false otherwise
   */
  async tryClickRecommendedVideo(targetUrl) {
    try {
      if (!youtubeView || !youtubeView.webContents || youtubeView.webContents.isDestroyed()) {
        return false;
      }

      console.log('Searching for recommended video:', targetUrl);

      // Extract video ID from target URL
      const targetVideoId = this.extractVideoId(targetUrl);
      if (!targetVideoId) {
        console.log('Could not extract video ID from URL:', targetUrl);
        return false;
      }

      // Execute JavaScript to find and click recommended video
      const clickResult = await youtubeView.webContents.executeJavaScript(`
        (function() {
          const targetVideoId = '${targetVideoId}';
          console.log('Looking for video ID:', targetVideoId);

          // Find all video links on the page
          const videoLinks = document.querySelectorAll('a[href*="/watch?v="], a[href*="youtu.be/"]');
          console.log('Found', videoLinks.length, 'video links on page');

          for (let link of videoLinks) {
            const href = link.href;
            let videoId = '';

            // Extract video ID from different URL formats
            if (href.includes('/watch?v=')) {
              const match = href.match(/[?&]v=([^&]+)/);
              videoId = match ? match[1] : '';
            } else if (href.includes('youtu.be/')) {
              const match = href.match(/youtu\\.be\\/([^?&]+)/);
              videoId = match ? match[1] : '';
            }

            if (videoId === targetVideoId) {
              console.log('Found matching recommended video link:', href);

              // Preserve any tracking parameters from the current page
              const currentUrl = new URL(window.location.href);
              const linkUrl = new URL(href, window.location.origin);

              // Copy tracking parameters if they exist
              const trackingParams = ['list', 'index', 'pp', 'si', 'feature', 'app', 'itct'];
              trackingParams.forEach(param => {
                if (currentUrl.searchParams.has(param)) {
                  linkUrl.searchParams.set(param, currentUrl.searchParams.get(param));
                }
              });

              // Update the link href with tracking parameters
              link.href = linkUrl.toString();

              // Click the link to navigate naturally
              console.log('Clicking recommended video with tracking:', link.href);
              link.click();
              return true;
            }
          }

          console.log('No matching recommended video found for ID:', targetVideoId);
          return false;
        })();
      `);

      if (clickResult) {
        console.log('Successfully clicked recommended video link');
        return true;
      } else {
        console.log('No matching recommended video found on current page');
        return false;
      }
    } catch (error) {
      console.error('Error trying to click recommended video:', error);
      return false;
    }
  }

  /**
   * Extract video ID from YouTube URL
   * @param {string} url - YouTube URL
   * @returns {string|null} - Video ID or null if not found
   */
  extractVideoId(url) {
    try {
      const urlObj = new URL(url);

      // Handle youtube.com/watch?v= format
      if (urlObj.hostname.includes('youtube.com') && urlObj.pathname === '/watch') {
        return urlObj.searchParams.get('v');
      }

      // Handle youtu.be/ format
      if (urlObj.hostname === 'youtu.be') {
        return urlObj.pathname.substring(1); // Remove leading slash
      }

      return null;
    } catch (error) {
      console.error('Error extracting video ID from URL:', url, error);
      return null;
    }
  }



  /**
   * Check if user is logged in to Spotify
   * @returns {Promise<boolean>} - True if user is logged in to Spotify
   */
  async isSpotifyLoggedIn() {
    try {
      if (!spotifyView || !spotifyView.webContents || spotifyView.webContents.isDestroyed()) {
        return false;
      }

      const loginStatus = await spotifyView.webContents.executeJavaScript(`
        (function() {
          const currentUrl = window.location.href;

          // Check for login form presence (indicates NOT logged in)
          const loginForm = document.querySelector('#login-username') ||
                           document.querySelector('input[name="username"]') ||
                           document.querySelector('input[type="email"]') ||
                           document.querySelector('.login-form') ||
                           document.querySelector('[data-testid="login-username"]') ||
                           document.querySelector('button[data-testid="login-button"]');

          // Check for user profile elements (indicates logged in)
          const userProfile = document.querySelector('.user-widget') ||
                             document.querySelector('[data-testid="user-widget"]') ||
                             document.querySelector('.profile-menu') ||
                             document.querySelector('.user-menu') ||
                             document.querySelector('[data-testid="user-menu"]');

          // Check for Spotify Web Player elements
          const webPlayer = document.querySelector('.now-playing') ||
                           document.querySelector('[data-testid="now-playing-widget"]') ||
                           document.querySelector('.player-controls') ||
                           document.querySelector('.playback-bar');

          // Simple determination: if we're on open.spotify.com and no login form, likely logged in
          const isOnSpotifyWeb = currentUrl.includes('open.spotify.com');
          const hasLoginForm = !!loginForm;
          const hasUserElements = !!userProfile || !!webPlayer;

          return !hasLoginForm && (hasUserElements || isOnSpotifyWeb);
        })();
      `);

      return loginStatus;
    } catch (error) {
      console.error('Error checking Spotify login status:', error);
      return false;
    }
  }





  async toggleSystemAudio() {
    try {
      isMuted = await audioController.toggleMute();
      this.updateTrayMenu();

      // Notify renderer process
      if (mainWindow && !mainWindow.isDestroyed()) {
        try {
          mainWindow.webContents.send('audio-state-changed', { isMuted });
        } catch (sendError) {
          console.warn('Failed to send audio state to renderer:', sendError.message);
        }
      }

      return isMuted;
    } catch (error) {
      console.error('Failed to toggle audio:', error);
      throw new Error(`Audio toggle failed: ${error.message}`);
    }
  }

  updateTrayMenu() {
    if (tray && !tray.isDestroyed()) {
      const contextMenu = Menu.buildFromTemplate([
        {
          label: 'YSViewer',
          type: 'normal',
          enabled: false
        },
        { type: 'separator' },
        {
          label: isMuted ? 'Unmute Application' : 'Mute Application',
          type: 'normal',
          click: () => this.toggleSystemAudio()
        },
        { type: 'separator' },
        {
          label: 'Show Window',
          type: 'normal',
          click: () => this.showMainWindow()
        },
        {
          label: 'Minimize to Dot',
          type: 'normal',
          click: () => this.minimizeToDot()
        },
        { type: 'separator' },
        {
          label: 'Quit',
          type: 'normal',
          click: () => {
            app.isQuiting = true;
            app.quit();
          }
        }
      ]);
      tray.setContextMenu(contextMenu);
    }
  }

  showMainWindow() {
    const startTime = Date.now();
    let result = null;
    let error = null;

    try {
      if (mainWindow) {
        if (isMinimized) {
          // Restore from minimized state
          mainWindow.setSize(1400, 800);
          mainWindow.center();
          isMinimized = false;
        }
        mainWindow.show();
        mainWindow.focus();
        result = { success: true, windowVisible: true, wasMinimized: isMinimized };
      } else {
        result = { success: false, error: 'No main window available' };
      }
      return result;
    } catch (err) {
      error = err;
      throw err;
    } finally {
      executionCollector.collect(
        'showMainWindow',
        {},
        result,
        error,
        {
          executionTime: Date.now() - startTime,
          hasMainWindow: !!mainWindow,
          wasMinimized: isMinimized
        }
      );
    }
  }

  minimizeToTray() {
    const startTime = Date.now();
    let result = null;
    let error = null;

    try {
      if (mainWindow) {
        mainWindow.hide();
        result = { success: true, windowHidden: true };
      } else {
        result = { success: false, error: 'No main window available' };
      }
      return result;
    } catch (err) {
      error = err;
      throw err;
    } finally {
      executionCollector.collect(
        'minimizeToTray',
        {},
        result,
        error,
        {
          executionTime: Date.now() - startTime,
          hasMainWindow: !!mainWindow
        }
      );
    }
  }

  minimizeToDot() {
    const startTime = Date.now();
    let result = null;
    let error = null;

    try {
      if (mainWindow) {
        const { width, height } = screen.getPrimaryDisplay().workAreaSize;
        mainWindow.setSize(50, 50); // Changed from 5x5 to 50x50
        mainWindow.setPosition(width - 50, height - 50);
        mainWindow.setAlwaysOnTop(true);
        mainWindow.setSkipTaskbar(true);
        isMinimized = true;
        result = {
          success: true,
          windowMinimized: true,
          position: { x: width - 50, y: height - 50 },
          size: { width: 50, height: 50 }
        };
      } else {
        result = { success: false, error: 'No main window available' };
      }
      return result;
    } catch (err) {
      error = err;
      throw err;
    } finally {
      executionCollector.collect(
        'minimizeToDot',
        {},
        result,
        error,
        {
          executionTime: Date.now() - startTime,
          hasMainWindow: !!mainWindow,
          screenSize: mainWindow ? screen.getPrimaryDisplay().workAreaSize : null
        }
      );
    }
  }











  async autoAcceptCookies(webContents, platform) {
    try {
      // Check if cookies have already been accepted for this platform
      const cookieAcceptanceKey = `cookiesAccepted_${platform}`;
      const alreadyAccepted = store.get(cookieAcceptanceKey, false);

      if (alreadyAccepted) {
        console.log(`Cookies already accepted for ${platform}, skipping auto-accept`);
        return;
      }

      console.log(`Attempting to auto-accept cookies for ${platform}`);

      let cookieAccepted = false;

      if (platform === 'YouTube') {
        // YouTube cookie acceptance
        cookieAccepted = await webContents.executeJavaScript(`
          (function() {
            // Try multiple selectors for YouTube cookie consent (updated for 2024)
            const selectors = [
              // Modern YouTube cookie consent selectors
              'button[aria-label*="Accept all"]',
              'button[aria-label*="Accept All"]',
              'button[aria-label*="ACCEPT ALL"]',
              'button[aria-label*="Accept"]',
              'button[aria-label*="accept"]',
              'button[aria-label*="I agree"]',
              'button[aria-label*="Agree"]',
              // YouTube specific selectors
              '.VfPpkd-LgbsSe[jsname="tWT92d"]',
              'button[jsname="tWT92d"]',
              'button[data-testid="accept-button"]',
              'button[data-testid="accept-all-button"]',
              // Generic consent selectors
              'button[id*="accept"]',
              'button[class*="accept"]',
              'button[class*="consent"]',
              // GDPR/Cookie banner frameworks
              '.fc-consent-root button[data-testid="fc-confirm-choices"]',
              '.fc-consent-root button[aria-label*="Consent"]',
              '#consent-bump button',
              '.consent-bump button',
              // OneTrust selectors
              '#onetrust-accept-btn-handler',
              '.onetrust-accept-btn-handler',
              'button[id*="onetrust"][id*="accept"]'
            ];

            for (let selector of selectors) {
              const button = document.querySelector(selector);
              if (button && button.offsetParent !== null && !button.disabled) {
                console.log('Found YouTube cookie accept button:', selector);
                button.click();
                return true;
              }
            }

            // Try finding by text content with more comprehensive search
            const buttons = document.querySelectorAll('button, [role="button"]');
            for (let button of buttons) {
              if (!button.offsetParent || button.disabled) continue;

              const text = button.textContent.toLowerCase().trim();
              const ariaLabel = (button.getAttribute('aria-label') || '').toLowerCase();
              const title = (button.getAttribute('title') || '').toLowerCase();

              const acceptTexts = [
                'accept all', 'accept all cookies', 'accept cookies',
                'i agree', 'agree', 'allow all', 'allow cookies',
                'ok', 'got it', 'continue', 'proceed'
              ];

              for (let acceptText of acceptTexts) {
                if (text.includes(acceptText) || ariaLabel.includes(acceptText) || title.includes(acceptText)) {
                  console.log('Found YouTube cookie button by text:', text || ariaLabel || title);
                  button.click();
                  return true;
                }
              }
            }

            return false;
          })();
        `);
      } else if (platform === 'Spotify') {
        // Spotify cookie acceptance
        cookieAccepted = await webContents.executeJavaScript(`
          (function() {
            // Try multiple selectors for Spotify cookie consent (updated for 2024)
            const selectors = [
              // Spotify specific selectors
              'button[data-testid="accept-all-cookies"]',
              'button[data-testid="accept-cookies"]',
              'button[data-testid="cookie-accept"]',
              'button[data-cy="accept-all-cookies"]',
              'button[data-cy="accept-cookies"]',
              // OneTrust framework (commonly used by Spotify)
              '#onetrust-accept-btn-handler',
              '.onetrust-accept-btn-handler',
              'button[id*="onetrust"][id*="accept"]',
              'button[class*="onetrust"][class*="accept"]',
              // CookieBot framework
              '#CybotCookiebotDialogBodyLevelButtonLevelOptinAllowAll',
              'button[id*="CybotCookiebot"][id*="Allow"]',
              // Generic consent selectors
              'button[id*="accept"]',
              'button[class*="accept"]',
              'button[class*="consent"]',
              'button[data-qa="accept"]',
              'button[data-qa="accept-all"]',
              // GDPR banner selectors
              '.gdpr-banner button[data-accept]',
              '.cookie-banner button[data-accept]',
              '.consent-banner button',
              // Quantcast Choice framework
              '.qc-cmp2-summary-buttons button[mode="primary"]',
              '.qc-cmp2-summary-buttons button:last-child'
            ];

            for (let selector of selectors) {
              const button = document.querySelector(selector);
              if (button && button.offsetParent !== null && !button.disabled) {
                console.log('Found Spotify cookie accept button:', selector);
                button.click();
                return true;
              }
            }

            // Try finding by text content with more comprehensive search
            const buttons = document.querySelectorAll('button, [role="button"], input[type="button"]');
            for (let button of buttons) {
              if (!button.offsetParent || button.disabled) continue;

              const text = button.textContent.toLowerCase().trim();
              const ariaLabel = (button.getAttribute('aria-label') || '').toLowerCase();
              const title = (button.getAttribute('title') || '').toLowerCase();
              const value = (button.getAttribute('value') || '').toLowerCase();

              const acceptTexts = [
                'accept all', 'accept all cookies', 'accept cookies',
                'allow all', 'allow cookies', 'allow all cookies',
                'i agree', 'agree', 'agree to all', 'agree & close',
                'ok', 'got it', 'continue', 'proceed',
                'yes, i agree', 'accept and continue',
                'accept & close', 'save & exit'
              ];

              for (let acceptText of acceptTexts) {
                if (text.includes(acceptText) || ariaLabel.includes(acceptText) ||
                    title.includes(acceptText) || value.includes(acceptText)) {
                  console.log('Found Spotify cookie button by text:', text || ariaLabel || title || value);
                  button.click();
                  return true;
                }
              }
            }

            return false;
          })();
        `);
      }

      // If cookies were successfully accepted, mark it as completed
      if (cookieAccepted) {
        store.set(cookieAcceptanceKey, true);
        console.log(`Cookies accepted for ${platform} - marked as completed, will not try again`);
      }
    } catch (error) {
      console.log(`Failed to auto-accept cookies for ${platform}:`, error.message);
    }
  }

  // Utility method to reset cookie acceptance flags (for testing/troubleshooting)
  resetCookieAcceptance(platform = null) {
    if (platform) {
      const cookieAcceptanceKey = `cookiesAccepted_${platform}`;
      store.delete(cookieAcceptanceKey);
      console.log(`Reset cookie acceptance flag for ${platform}`);
    } else {
      // Reset all platforms
      store.delete('cookiesAccepted_YouTube');
      store.delete('cookiesAccepted_Spotify');
      console.log('Reset cookie acceptance flags for all platforms');
    }
  }







  /**
   * Logout from Spotify
   */
  async logoutFromSpotify() {
    try {
      if (!spotifyView || !spotifyView.webContents || spotifyView.webContents.isDestroyed()) {
        throw new Error('Spotify view not available');
      }

      console.log('[Spotify Logout] Starting logout process');

      // Navigate to Spotify logout URL
      spotifyView.webContents.loadURL('https://accounts.spotify.com/logout');

      // Wait a bit for logout to process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Clear session data
      await spotifyView.webContents.session.clearStorageData({
        storages: ['cookies', 'localstorage', 'sessionstorage', 'indexdb', 'websql']
      });

      console.log('[Spotify Logout] Logout completed successfully');
    } catch (error) {
      console.error('[Spotify Logout] Error during logout:', error);
      throw error;
    }
  }

  /**
   * Wait for Spotify login to complete
   */
  async waitForSpotifyLogin() {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Login verification timeout'));
      }, 15000);

      const checkLoginStatus = async () => {
        try {
          const currentUrl = spotifyView.webContents.getURL();

          if (currentUrl.includes('open.spotify.com') ||
              currentUrl.includes('spotify.com/track') ||
              currentUrl.includes('spotify.com/playlist') ||
              currentUrl.includes('spotify.com/browse')) {
            clearTimeout(timeout);
            resolve();
          } else {
            setTimeout(checkLoginStatus, 1000);
          }
        } catch (error) {
          clearTimeout(timeout);
          reject(error);
        }
      };

      setTimeout(checkLoginStatus, 3000);
    });
  }

  /**
   * Attempt automatic replay of recorded login flow
   * @param {string} targetUrl - URL to navigate to after login
   * @param {Object} recording - Recorded login interactions
   * @returns {Promise<boolean>} - True if replay was successful
   */
  async attemptAutomaticReplay(targetUrl, recording) {
    try {
      console.log('[Spotify Replay] Starting automatic login replay');

      // Load Spotify login page
      spotifyView.webContents.loadURL('https://accounts.spotify.com/login');

      // Wait for page to load
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Replay the recorded interactions
      const success = await this.spotifyReplayer.replayInteractions(
        recording.interactions,
        spotifyView.webContents
      );

      if (!success) {
        console.warn('[Spotify Replay] Interaction replay failed');
        return false;
      }

      console.log('[Spotify Replay] Interactions replayed, checking login status');

      // Wait a bit for login to process
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Check if login was successful
      const isLoggedIn = await this.isSpotifyLoggedIn();
      if (isLoggedIn) {
        console.log('[Spotify Replay] Automatic login successful');
        spotifyView.webContents.loadURL(targetUrl);
        return true;
      } else {
        console.warn('[Spotify Replay] Login verification failed');
        return false;
      }

    } catch (error) {
      console.error('[Spotify Replay] Error during automatic replay:', error);
      return false;
    }
  }

  /**
   * Show login failure warning to user
   * @param {string} message - Warning message to display
   */
  async showLoginFailureWarning(message) {
    try {
      // Send notification to renderer process
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('show-notification', {
          message: message,
          type: 'error',
          duration: 5000
        });
      }

      console.warn('[Spotify Login Warning]', message);
    } catch (error) {
      console.error('[Spotify Login Warning] Error showing warning:', error);
    }
  }
}

// App event handlers
app.whenReady().then(() => {
  YSViewerApp.getInstance();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    YSViewerApp.getInstance().showMainWindow();
  }
});

// IPC handlers
ipcMain.handle('get-app-state', () => {
  return {
    isMuted,
    isMinimized
  };
});

ipcMain.handle('toggle-audio', async () => {
  const startTime = Date.now();
  let result = null;
  let error = null;

  try {
    const app = YSViewerApp.getInstance();
    await app.toggleSystemAudio();
    result = { success: true, isMuted };
    return result;
  } catch (err) {
    error = err;
    console.error('IPC toggle-audio error:', err);
    result = { success: false, error: err.message };
    return result;
  } finally {
    executionCollector.collect(
      'ipc_toggle_audio',
      {},
      result,
      error,
      {
        executionTime: Date.now() - startTime,
        previousMuteState: !isMuted
      }
    );
  }
});

ipcMain.handle('minimize-to-tray', () => {
  try {
    const app = YSViewerApp.getInstance();
    app.minimizeToTray();
    return { success: true };
  } catch (error) {
    console.error('IPC minimize-to-tray error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('close-application', () => {
  try {
    app.isQuiting = true;
    app.quit();
    return { success: true };
  } catch (error) {
    console.error('IPC close-application error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('minimize-to-dot', () => {
  try {
    const app = YSViewerApp.getInstance();
    app.minimizeToDot();
    return { success: true };
  } catch (error) {
    console.error('IPC minimize-to-dot error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('show-window', () => {
  try {
    const app = YSViewerApp.getInstance();
    app.showMainWindow();
    return { success: true };
  } catch (error) {
    console.error('IPC show-window error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('reset-cookie-acceptance', (_, platform) => {
  try {
    const app = YSViewerApp.getInstance();
    app.resetCookieAcceptance(platform);
    return { success: true };
  } catch (error) {
    console.error('IPC reset-cookie-acceptance error:', error);
    return { success: false, error: error.message };
  }
});

// Spotify login IPC handlers
ipcMain.handle('spotify-login', async (_, { username, password }) => {
  try {
    const app = YSViewerApp.getInstance();
    await app.loginToSpotifyWithCredentials(username, password);
    return { success: true };
  } catch (error) {
    console.error('IPC spotify-login error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('spotify-record-login', async () => {
  try {
    const app = YSViewerApp.getInstance();
    await app.startSpotifyLoginRecording();
    return { success: true };
  } catch (error) {
    console.error('IPC spotify-record-login error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('spotify-logout', async () => {
  try {
    const app = YSViewerApp.getInstance();
    await app.logoutFromSpotify();
    return { success: true };
  } catch (error) {
    console.error('IPC spotify-logout error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('verify-mute-state', () => {
  try {
    audioController.verifyMuteState();
    return { success: true };
  } catch (error) {
    console.error('IPC verify-mute-state error:', error);
    return { success: false, error: error.message };
  }
});



ipcMain.handle('reset-ad-skipper-stats', () => {
  try {
    youtubeAdSkipper.resetStats();
    return { success: true };
  } catch (error) {
    console.error('IPC reset-ad-skipper-stats error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('debug-ad-detection', async () => {
  try {
    await youtubeAdSkipper.debugAdDetection();
    return { success: true };
  } catch (error) {
    console.error('IPC debug-ad-detection error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-progress-stats', () => {
  try {
    const stats = videoProgressMonitor.getProgressStats();
    return { success: true, stats };
  } catch (error) {
    console.error('IPC get-progress-stats error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-progress-history', () => {
  try {
    const history = videoProgressMonitor.getProgressHistory();
    return { success: true, history };
  } catch (error) {
    console.error('IPC get-progress-history error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('toggle-progress-monitor', () => {
  try {
    if (videoProgressMonitor.isEnabled) {
      videoProgressMonitor.disable();
    } else {
      videoProgressMonitor.enable();
    }
    return { success: true, isEnabled: videoProgressMonitor.isEnabled };
  } catch (error) {
    console.error('IPC toggle-progress-monitor error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('reset-progress-monitor', () => {
  try {
    videoProgressMonitor.reset();
    return { success: true };
  } catch (error) {
    console.error('IPC reset-progress-monitor error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-ad-detection-history', () => {
  try {
    const history = youtubeAdSkipper.getAdDetectionHistory();
    return { success: true, history };
  } catch (error) {
    console.error('IPC get-ad-detection-history error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-skip-attempt-history', () => {
  try {
    const history = youtubeAdSkipper.getSkipAttemptHistory();
    return { success: true, history };
  } catch (error) {
    console.error('IPC get-skip-attempt-history error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-ad-skipper-mock-data', () => {
  try {
    const mockData = youtubeAdSkipper.getMockData();
    return { success: true, mockData };
  } catch (error) {
    console.error('IPC get-ad-skipper-mock-data error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('clear-ad-skipper-mock-data', () => {
  try {
    youtubeAdSkipper.clearMockData();
    return { success: true };
  } catch (error) {
    console.error('IPC clear-ad-skipper-mock-data error:', error);
    return { success: false, error: error.message };
  }
});
